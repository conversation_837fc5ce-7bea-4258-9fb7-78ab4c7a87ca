import 'package:korrency/core/lib/lib.dart';

class ConfirmationSubtitleText extends StatelessWidget {
  const ConfirmationSubtitleText({
    Key? key,
    required this.startText,
    this.coloredText,
    this.endText,
  }) : super(key: key);

  final String startText;
  final String? coloredText;
  final String? endText;

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        text: startText,
        style: AppTypography.text16.copyWith(
          color: AppColors.gray700,
          fontWeight: FontWeight.w400,
          height: 2,
        ),
        children: [
          TextSpan(
            text: coloredText,
            style: AppTypography.text16.copyWith(
              color: AppColors.blue000,
              fontWeight: FontWeight.w400,
              height: 1.2,
            ),
          ),
          TextSpan(
            text: endText,
            style: AppTypography.text16.copyWith(
              color: AppColors.gray700,
              fontWeight: FontWeight.w400,
              height: 1.2,
            ),
          ),
        ],
      ),
    );
  }
}
