import 'package:korrency/core/lib/lib.dart';

class CurrencyPrefix extends StatelessWidget {
  const CurrencyPrefix({
    Key? key,
    required this.countryFlag,
    required this.countryDialCode,
  }) : super(key: key);

  final String countryFlag;
  final String countryDialCode;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        imageHelper(
          countryFlag,
          height: Sizer.height(20),
          width: Sizer.width(20),
        ),
        const XBox(4),
        const Icon(
          Icons.expand_more,
          color: AppColors.gray500,
          size: 30,
        ),
        Text(
          countryDialCode,
          style: AppTypography.text16.copyWith(
            color: AppColors.gray500,
          ),
        ),
      ],
    );
  }
}
