import 'package:korrency/core/lib/lib.dart';

class AuthTextSubTitle extends StatelessWidget {
  const AuthTextSubTitle({
    Key? key,
    required this.title,
    this.titleColor,
    this.titleFontSize,
    required this.subtitle,
    this.coloredText,
    this.lastText,
    this.subtitleColor,
    this.colorTextColor,
    this.colortextFontWeight,
    this.subtitleFontSize,
    this.useRichText = false,
  }) : super(key: key);

  final String title;
  final Color? titleColor;
  final double? titleFontSize;
  final String subtitle;
  final String? coloredText;
  final String? lastText;
  final Color? subtitleColor;
  final Color? colorTextColor;
  final FontWeight? colortextFontWeight;
  final double? subtitleFontSize;
  final bool useRichText;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: titleColor ?? AppColors.black,
            fontSize: Sizer.text(titleFontSize ?? 20),
          ),
        ),
        const YBox(4),
        useRichText
            ? RichText(
                text: TextSpan(
                  text: subtitle,
                  style: TextStyle(
                    color: subtitleColor ?? AppColors.gray700,
                    fontSize: Sizer.text(subtitleFontSize ?? 15),
                    fontFamily: 'Inter',
                    height: 1.2,
                  ),
                  children: [
                    TextSpan(
                      text: coloredText,
                      style: TextStyle(
                        color: colorTextColor ?? AppColors.blue700,
                        fontSize: Sizer.text(subtitleFontSize ?? 15),
                        fontFamily: 'Inter',
                        fontWeight: colortextFontWeight,
                        height: 1.2,
                      ),
                    ),
                    TextSpan(
                      text: lastText,
                      style: TextStyle(
                        color: subtitleColor ?? AppColors.gray700,
                        fontSize: Sizer.text(subtitleFontSize ?? 15),
                        fontFamily: 'Inter',
                        height: 1.2,
                      ),
                    ),
                  ],
                ),
              )
            : Text(
                subtitle,
                style: TextStyle(
                  color: subtitleColor ?? AppColors.gray700,
                  fontSize: Sizer.text(subtitleFontSize ?? 15),
                  fontFamily: 'Inter',
                  height: 1.2,
                ),
              ),
      ],
    );
  }
}
