import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/core/lib/lib.dart';
import 'package:korrency/ui/components/components.dart';

class CustomListViews {
  static Widget viewWithSubTitle({
    required IconData icon,
    required String title,
    required String subTitle,
    VoidCallback? onTap,
    final EdgeInsetsGeometry? padding,
  }) {
    return InkWell(
      onTap: onTap,
      child: ContainerWithBluewishBg(
        padding: padding ??
            EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
              vertical: Sizer.height(12),
            ),
        child: Row(
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(
                top: Sizer.height(3),
              ),
              child: Icon(
                icon,
                size: Sizer.radius(20),
              ),
            ),
            const XBox(10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.textBlack900,
                  ),
                ),
                Text(
                  subTitle,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textBlack800,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  static Widget view({
    bool isSvg = false,
    bool isNetworkSvg = false,
    dynamic icon,
    required String title,
    VoidCallback? onTap,
    final EdgeInsetsGeometry? padding,
  }) {
    return InkWell(
      onTap: onTap,
      child: ContainerWithBluewishBg(
        padding: padding,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              // padding: EdgeInsets.only(
              //   top: Sizer.height(3),
              // ),
              child: isSvg
                  ? isNetworkSvg
                      ? SizedBox(
                          height: Sizer.height(23),
                          width: Sizer.width(23),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(100),
                            child: MyCachedNetworkImage(
                              height: Sizer.height(23),
                              width: Sizer.width(23),
                              imageUrl: icon,
                            ),
                          ),
                        )
                      : svgHelper(
                          AppSvgs.creditKorrency,
                          height: Sizer.height(23),
                          width: Sizer.width(23),
                        )
                  : icon is IconData
                      ? Icon(
                          icon,
                          size: Sizer.radius(20),
                        )
                      : imageHelper(
                          icon,
                          height: Sizer.height(24),
                          width: Sizer.width(24),
                        ),
            ),
            const XBox(16),
            Text(
              title,
              style: AppTypography.text16.copyWith(
                color: AppColors.textBlack900,
              ),
            ),
            const Spacer(),
            Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Iconsax.arrow_right_3,
                color: AppColors.iconBlack800,
                size: Sizer.radius(24),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget currencyListText({
    bool showImage = false,
    bool showCopyIcon = false,
    required String leftText,
    required String rightText,
    FontWeight? rightFontWeight,
    Color? rightTextColor,
    String? imgPath,
    EdgeInsetsGeometry? margin,
    VoidCallback? onCopy,
  }) {
    return Container(
      margin: margin ??
          EdgeInsets.only(
            bottom: Sizer.height(24),
          ),
      child: Row(
        children: [
          Expanded(
            flex: 4,
            child: Text(
              leftText,
              style: AppTypography.text13.copyWith(
                color: AppColors.textGray,
              ),
            ),
          ),
          const XBox(10),
          Expanded(
            flex: 4,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (showImage)
                  imageHelper(
                    imgPath!,
                    height: Sizer.height(14),
                    width: Sizer.width(20),
                  ),
                if (showImage) const XBox(6),
                Expanded(
                  child: Text(
                    rightText,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.end,
                    style: AppTypography.text13.copyWith(
                      color: rightTextColor ?? AppColors.blue600,
                      fontWeight: rightFontWeight,
                    ),
                  ),
                ),
                if (showCopyIcon) const XBox(4),
                if (showCopyIcon)
                  InkWell(
                    onTap: onCopy,
                    child: Icon(
                      Iconsax.copy,
                      size: Sizer.radius(20),
                      color: AppColors.iconBlack800,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
