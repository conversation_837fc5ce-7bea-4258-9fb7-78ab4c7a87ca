import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/lib/lib.dart';

class WalletListTile extends StatelessWidget {
  const WalletListTile({
    Key? key,
    required this.title,
    this.subTitle,
    this.currencyIcon,
    this.icon,
    this.isSelected = false,
    this.showTrailing = false,
    this.showTrailingWidget = true,
    this.useNetworkSvg = false,
    this.trailingWidget,
    this.subTitleStyle,
    this.titleStyle,
    this.trailingIconSize,
    this.titleFontSize,
    this.titleMaxLines,
    this.onTap,
  }) : super(key: key);

  final String title;
  final String? subTitle;
  final String? currencyIcon;
  final IconData? icon;
  final bool isSelected;
  final bool showTrailing;
  final bool showTrailingWidget;
  final bool useNetworkSvg;
  final Widget? trailingWidget;
  final double? titleFontSize;
  final int? titleMaxLines;
  final double? trailingIconSize;
  final TextStyle? titleStyle;
  final TextStyle? subTitleStyle;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          if (currencyIcon != null)
            _buildCurrencyIcon(useNetworkSvg: useNetworkSvg),
          if (currencyIcon != null) const SizedBox(width: 16),
          Expanded(child: _buildTextContent()),
          if (showTrailingWidget) _buildTrailingWidget(),
        ],
      ),
    );
  }

  Widget _buildCurrencyIcon({bool? useNetworkSvg}) {
    return Container(
      padding: EdgeInsets.only(top: Sizer.height(3)),
      child: useNetworkSvg ?? false
          ? SvgPicture.network(
              currencyIcon ?? '',
              width: Sizer.width(20),
              height: Sizer.height(14),
            )
          : imageHelper(
              currencyIcon ?? '',
              width: Sizer.width(20),
              height: Sizer.height(14),
              fit: BoxFit.cover,
            ),
    );
  }

  Widget _buildTextContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          maxLines: titleMaxLines ?? 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: Sizer.text(titleFontSize ?? 16),
            color: AppColors.textBlack900,
          ),
        ),
        if (subTitle != null)
          Text(
            subTitle!,
            style: subTitleStyle ??
                AppTypography.text14.copyWith(
                  color: AppColors.blue800,
                  fontWeight: FontWeight.w600,
                ),
          ),
      ],
    );
  }

  Widget _buildTrailingWidget() {
    if (showTrailing) {
      return trailingWidget!;
    } else if (isSelected) {
      return Icon(
        Icons.check_circle,
        size: (trailingIconSize != null ? trailingIconSize! + 4 : 24),
        color: AppColors.blue800,
      );
    } else {
      return Container(
        width: Sizer.width(trailingIconSize ?? 20),
        height: Sizer.height(trailingIconSize ?? 20),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(
            color: AppColors.blue800,
            width: 2,
          ),
        ),
      );
    }
  }
}
