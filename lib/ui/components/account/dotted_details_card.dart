import 'package:dotted_border/dotted_border.dart';
import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/lib/lib.dart';
import 'package:korrency/ui/components/components.dart';

class DottedDetailCard extends StatelessWidget {
  const DottedDetailCard({
    Key? key,
    required this.virtualAcctNumber,
    required this.virtualAcctName,
    required this.virtualAcctBank,
    this.onCopy,
    this.onShare,
  }) : super(key: key);

  final String virtualAcctNumber;
  final String virtualAcctName;
  final String virtualAcctBank;
  final VoidCallback? onCopy;
  final VoidCallback? onShare;

  @override
  Widget build(BuildContext context) {
    return DottedBorder(
      dashPattern: const [8, 5],
      strokeWidth: 2,
      borderType: BorderType.RRect,
      radius: const Radius.circular(4),
      color: AppColors.dottedColor,
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
        vertical: Sizer.height(12),
      ),
      child: <PERSON><PERSON><PERSON><PERSON>(
        width: Sizer.screenWidth,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Account Number',
                      style: AppTypography.text12.copyWith(
                        color: AppColors.textGray,
                      ),
                    ),
                    const YBox(4),
                    Text(
                      virtualAcctNumber,
                      style: AppTypography.text20b.copyWith(
                        color: AppColors.blue600,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                CopyShareBtn(
                  color: AppColors.blue600,
                  onTap: onCopy,
                ),
              ],
            ),
            const YBox(16),
            Text(
              'Bank Name',
              style: AppTypography.text12.copyWith(
                color: AppColors.textGray,
              ),
            ),
            const YBox(4),
            Text(
              virtualAcctBank,
              style: AppTypography.text12b.copyWith(
                color: AppColors.textGray,
              ),
            ),
            const YBox(16),
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Account Name',
                      style: AppTypography.text12.copyWith(
                        color: AppColors.textGray,
                      ),
                    ),
                    const YBox(4),
                    Text(
                      virtualAcctName,
                      style: AppTypography.text12b.copyWith(
                        color: AppColors.textGray,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                CopyShareBtn(
                  title: "Share",
                  icon: Iconsax.send_2,
                  color: AppColors.blue600,
                  onTap: onShare,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
