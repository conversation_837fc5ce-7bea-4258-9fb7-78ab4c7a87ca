import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SelectCurrencySheet extends StatefulWidget {
  const SelectCurrencySheet({
    super.key,
    this.fromConvert = false,
    this.isSendMoney = false,
  });

  final bool fromConvert;
  final bool isSendMoney;

  @override
  State<SelectCurrencySheet> createState() => _SelectCurrencySheetState();
}

class _SelectCurrencySheetState extends State<SelectCurrencySheet> {
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      height: Sizer.screenHeight * 0.6,
      child: Column(
        children: [
          const YBox(20),
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Icons.close,
                size: Sizer.radius(23),
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(
              top: Sizer.height(20),
            ),
            alignment: Alignment.center,
            child: Text(
              "Select Currency",
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          // const YBox(25),
          Consumer<CurrencyVM>(builder: (context, vm, _) {
            // fromConvertWallet recipientCurrenciess
            final sendMoneyVM = context.read<SendMoneyVM>();
            final sendMoneyCurrencies =
                sendMoneyVM.fromConvertWallet?.currency?.recipientCurrencies;
            return Expanded(
              child: ListView.separated(
                padding: EdgeInsets.only(
                  top: Sizer.height(20),
                  bottom: Sizer.height(80),
                ),
                shrinkWrap: true,
                itemCount: widget.isSendMoney
                    ? sendMoneyCurrencies?.length ?? 0
                    : vm.currencies.length,
                itemBuilder: (context, i) {
                  var currency = vm.currencies[i];

                  return InkWell(
                    onTap: () {
                      sendMoneyCurrencySelection(
                          i,
                          widget.isSendMoney
                              ? sendMoneyCurrencies
                              : vm.currencies);
                    },
                    child: ContainerWithBluewishBg(
                      padding: EdgeInsets.symmetric(
                        vertical: Sizer.height(20),
                        horizontal: Sizer.width(16),
                      ),
                      child: WalletListTile(
                        title: widget.isSendMoney
                            ? (sendMoneyCurrencies?[i].code ?? '')
                            : currency.code ?? "",
                        currencyIcon: widget.isSendMoney
                            ? (sendMoneyCurrencies?[i].flag ?? '')
                            : currency.flag ?? "",
                        useNetworkSvg: true,
                        showTrailingWidget: false,
                        icon: Icons.check_circle,
                      ),
                    ),
                  );
                },
                separatorBuilder: (context, index) => const YBox(16),
              ),
            );
          }),
        ],
      ),
    );
  }

  /// No need for this, as i am already using SelectWalletCurrencySheet
  /// to handle for conversion
  // void handleCurrencySelection(int index, CurrencyVM currencyVM) {
  //   var convertMoneyVM = context.read<ConvertMoneyVM>();
  //   var walletVM = context.read<WalletVM>();
  //   if (walletVM.walletList.length == 2) {
  //     if (widget.fromConvert) {
  //       convertMoneyVM
  //           .setFromConvertWallet(currencyVM.currencies[index].code ?? "");
  //       // convertMoneyVM.setToConvertWallet(
  //       //     currencyVM.currencies[index == 0 ? 1 : 0].code ?? "");
  //     } else {
  //       convertMoneyVM
  //           .setToConvertWallet(currencyVM.currencies[index].code ?? "");
  //       // convertMoneyVM.setFromConvertWallet(
  //       //     currencyVM.currencies[index == 0 ? 1 : 0].code ?? "");
  //     }
  //   } else {
  //     if (widget.fromConvert) {
  //       convertMoneyVM
  //           .setFromConvertWallet(currencyVM.currencies[index].code ?? "");
  //     } else {
  //       convertMoneyVM
  //           .setToConvertWallet(currencyVM.currencies[index].code ?? "");
  //     }
  //   }
  //   // walletVM.getConversionRate();
  //   convertMoneyVM
  //     ..resetData()
  //     ..getConversionRate();
  //   Navigator.pop(context);
  // }

  void sendMoneyCurrencySelection(int index, List<Currency>? currencies) {
    var sendMoneyVM = context.read<SendMoneyVM>();
    var walletVM = context.read<WalletVM>();
    final fromCurrency = sendMoneyVM.fromConvertWallet;
    final recipientCurrency = sendMoneyVM.recipientCurrency;

    if (widget.fromConvert) {
      if (recipientCurrency?.code != currencies?[index].code) {
        sendMoneyVM.setFromConvertWallet(
          code: currencies?[index].code ?? "",
          walletList: walletVM.walletList,
        );
      } else {
        FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning,
          message: "Please select a different currency",
        );

        return;
      }
    } else {
      if (fromCurrency?.currency?.code != currencies?[index].code || (fromCurrency?.currency?.code == CurrencyConstant.cadCurrency && fromCurrency?.currency?.code != recipientCurrency?.code)) {
        sendMoneyVM.setRecipientCurrency(currencies![index]);
      } else {
        FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning,
          message: "Please select a different currency",
        );
        return;
      }
    }
    // sendMoneyVM
    //   ..getFees()
    //   ..getConversionRate();
    Navigator.pop(context);
  }
}
