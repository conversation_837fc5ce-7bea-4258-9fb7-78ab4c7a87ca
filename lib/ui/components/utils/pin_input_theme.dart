import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:korrency/core/lib/helpers/sizer.dart';
import 'package:korrency/core/lib/themes/themes.dart';
import 'package:pinput/pinput.dart';

class PinInputTheme {
  static defaultPinTheme({double? borderRadius, Color? bgColor}) {
    return PinTheme(
      width: 47.w,
      height: 47.w,
      textStyle: TextStyle(
          fontSize: 15.sp,
          color: AppColors.baseBlack,
          fontWeight: FontWeight.w500),
      decoration: BoxDecoration(
        color: bgColor ?? AppColors.gray300,
        border: Border.all(
          color: AppColors.gray300,
          width: 0,
        ),
        borderRadius: BorderRadius.circular(borderRadius ?? 4.r),
      ),
    );
  }

  static errorPinTheme() {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(width: 1, color: AppColors.red),
    );
  }

  static followPinTheme({double? borderRadius}) {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(width: Sizer.width(0.73), color: AppColors.gray500),
      borderRadius: BorderRadius.circular(borderRadius ?? 4.r),
      color: AppColors.bgWhite,
    );
  }

  static focusFillPinTheme() {
    return defaultPinTheme().copyDecorationWith(
        border: Border.all(width: 0, color: AppColors.gray500),
        color: AppColors.lightBlue.withOpacity(0.1));
  }

  static changePinTheme() {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(width: 0, color: AppColors.primaryLightBlue),
      color: AppColors.primaryLightBlue,
    );
  }
}
