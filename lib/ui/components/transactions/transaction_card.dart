import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/lib/lib.dart';

class TransactionTab extends StatelessWidget {
  const TransactionTab({
    Key? key,
    this.onTabChange,
    required this.text,
    this.isActive = false,
  }) : super(key: key);

  final VoidCallback? onTabChange;
  final String text;
  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTabChange,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(10),
          vertical: Sizer.height(8),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(
            Sizer.radius(4),
          ),
          border: Border.all(
            color: AppColors.opacityWhite,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text,
              style: TextStyle(
                fontSize: Sizer.text(12),
                color: AppColors.gray500,
              ),
            ),
            const XBox(4),
            Icon(
              Iconsax.arrow_down_1,
              size: Sizer.radius(20),
            )
          ],
        ),
      ),
    );
  }
}
