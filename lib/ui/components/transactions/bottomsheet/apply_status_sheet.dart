import 'package:korrency/core/lib/lib.dart';
import 'package:korrency/core/vm/transactions/transaction_vm.dart';
import 'package:korrency/ui/components/components.dart';

class ApplyStatusSheet extends StatefulWidget {
  const ApplyStatusSheet({Key? key}) : super(key: key);

  @override
  State<ApplyStatusSheet> createState() => _ApplyStatusSheetState();
}

class _ApplyStatusSheetState extends State<ApplyStatusSheet> {
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      height: Sizer.screenHeight * 0.59,
      child: Consumer<TransactionVM>(builder: (context, vm, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Choose Status",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    size: Sizer.radius(23),
                  ),
                )
              ],
            ),
            const YBox(25),
            InkWell(
              onTap: () {
                vm.setStatusFilter(value: StatusType.success.name);
              },
              child: ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                child: WalletListTile(
                  title: "Successful",
                  isSelected: vm.statusFilter.contains(StatusType.success.name),
                ),
              ),
            ),
            const YBox(20),
            InkWell(
              onTap: () {
                vm.setStatusFilter(value: StatusType.pending.name);
              },
              child: ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                child: WalletListTile(
                  title: "Pending",
                  isSelected: vm.statusFilter.contains(StatusType.pending.name),
                ),
              ),
            ),
            const YBox(20),
            InkWell(
              onTap: () {
                vm.setStatusFilter(value: StatusType.fail.name);
              },
              child: ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                child: WalletListTile(
                  title: "Failed",
                  isSelected: vm.statusFilter.contains(StatusType.fail.name),
                ),
              ),
            ),
            const Spacer(),
            CustomBtn.solid(
              onTap: () {
                _applyFilter();
                Navigator.pop(context);
                vm.setSelectedCurrency(null);
                vm.clearDates();
              },
              online: vm.statusFilter.isNotEmpty,
              text: "Apply Filter",
            ),
            const YBox(40),
          ],
        );
      }),
    );
  }

  _applyFilter() {
    var transactionVM = context.read<TransactionVM>();
    transactionVM.getFilteredTransactions(
      status: transactionVM.statusFilter,
    );
  }
}
