import 'package:korrency/core/lib/lib.dart';

class TransferCard extends StatelessWidget {
  const TransferCard({
    Key? key,
    required this.svgPath,
    required this.title,
    this.isOutlined = false,
    this.onTap,
  }) : super(key: key);

  final String svgPath;
  final String title;
  final bool isOutlined;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(13),
          vertical: Sizer.height(8),
        ),
        decoration: BoxDecoration(
            color: isOutlined ? AppColors.transparent : AppColors.bgWhite,
            borderRadius: BorderRadius.circular(Sizer.width(30)),
            border: Border.all(
              color: isOutlined ? AppColors.white : AppColors.bgWhite,
              width: Sizer.width(1),
            )),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            isOutlined
                ? Icon(
                    Icons.add,
                    size: Sizer.radius(24),
                    color: isOutlined ? AppColors.white : AppColors.textBlack,
                  )
                : svgHelper(
                    svgPath,
                    height: Sizer.height(24),
                    width: Sizer.width(24),
                  ),
            const XBox(6),
            Text(
              title,
              style: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w500,
                color: isOutlined ? AppColors.white : AppColors.textBlack,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
