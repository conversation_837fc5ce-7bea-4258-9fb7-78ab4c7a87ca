import 'package:korrency/core/lib/lib.dart';
import 'package:shimmer/shimmer.dart';

class HomeLoadState extends StatelessWidget {
  const HomeLoadState({super.key});

  @override
  Widget build(BuildContext context) {
    return const Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        YB<PERSON>(25),
        CustomShimmer(
          child: TextHeaderShimmer(),
        ),
        <PERSON><PERSON><PERSON>(25),
        CustomShimmer(
          child: TransactionCardShimmer(),
        ),
        YBox(25),
        CustomShimmer(
          child: TextHeaderShimmer(),
        ),
        YBox(25),
        CustomShimmer(
          child: CurrencyCardShimmer(),
        ),
      ],
    );
  }
}

class CustomShimmer extends StatelessWidget {
  const CustomShimmer({
    Key? key,
    required this.child,
  }) : super(key: key);

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: child,
    );
  }
}

class TextHeaderShimmer extends StatelessWidget {
  const TextHeaderShimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(24),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            height: 30,
            width: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              color: Colors.white,
            ),
          ),
          Container(
            height: 30,
            width: 70,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

class TransactionCardShimmer extends StatelessWidget {
  const TransactionCardShimmer({
    Key? key,
    this.itemCount,
    this.padding,
  }) : super(key: key);

  final int? itemCount;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      padding: padding ??
          EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (ctx, i) => Row(
        children: [
          Container(
            height: Sizer.height(50),
            width: Sizer.width(50),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          const XBox(10),
          Expanded(
            child: Container(
              height: Sizer.height(40),
              color: Colors.white, // Optional: match your background color
              // Add shimmer effect here
            ),
          ),
        ],
      ),
      separatorBuilder: (ctx, _) => const YBox(20),
      itemCount: itemCount ?? 2,
    );
  }
}

class CurrencyCardShimmer extends StatelessWidget {
  const CurrencyCardShimmer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: Sizer.width(24),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: List.generate(
            5,
            (index) => Container(
              width: 80,
              height: 80,
              margin: const EdgeInsets.only(right: 24),
              color: Colors.white, // Optional: match your background color
              // Add shimmer effect here
            ),
          ),
        ),
      ),
    );
  }
}
