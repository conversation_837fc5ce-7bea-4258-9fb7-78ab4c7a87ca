import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:korrency/core/lib/lib.dart';

class SwipeIndicator extends StatelessWidget {
  final bool isActive;
  final Color activeColor;

  const SwipeIndicator({
    super.key,
    required this.isActive,
    this.activeColor = AppColors.primaryBlue,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: 8.h,
      width: isActive ? 18.w : 8.w,
      decoration: BoxDecoration(
        color: isActive ? activeColor : AppColors.limitColor,
        borderRadius: BorderRadius.circular(Sizer.radius(6)),
        shape: BoxShape.rectangle,
      ),
    );
  }
}
