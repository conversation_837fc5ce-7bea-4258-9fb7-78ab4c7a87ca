import 'package:korrency/core/lib/lib.dart';

class TextRow extends StatelessWidget {
  const TextRow({
    Key? key,
    required this.text,
    this.value,
    this.isSelected = false,
    this.hasValue = false,
    this.width,
    this.height,
    this.onTap,
  }) : super(key: key);

  final String text;
  final String? value;
  final bool isSelected;
  final bool hasValue;
  final double? width;
  final double? height;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: height,
        width: width,
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(10),
        ),
        decoration: BoxDecoration(
          color: !isSelected ? AppColors.gray100 : AppColors.primaryBlue,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Text(
              text,
              style: AppTypography.text13.copyWith(
                color: !isSelected ? AppColors.textBlack700 : AppColors.bgWhite,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (hasValue) const XBox(8),
            if (hasValue)
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(4),
                  vertical: Sizer.height(2),
                ),
                decoration: BoxDecoration(
                  color: AppColors.bgWhite,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  value ?? '',
                  style: AppTypography.text8.copyWith(
                    color: AppColors.blue800,
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}
