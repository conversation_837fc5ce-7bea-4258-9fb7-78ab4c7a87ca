import 'package:korrency/core/lib/lib.dart';

class RowText extends StatelessWidget {
  const RowText({
    Key? key,
    required this.leftText,
    required this.rightText,
    this.isBold = false,
  }) : super(key: key);

  final String leftText;
  final String rightText;
  final bool isBold;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          leftText,
          style: AppTypography.text12.copyWith(
            color: AppColors.textGray,
            fontWeight: FontWeight.w400,
          ),
        ),
        const Spacer(),
        Text(
          rightText,
          style: AppTypography.text12.copyWith(
            color: AppColors.blue800,
            fontWeight: isBold ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
