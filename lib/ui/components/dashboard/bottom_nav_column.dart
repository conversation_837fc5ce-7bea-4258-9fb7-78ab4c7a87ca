// ignore_for_file: deprecated_member_use

import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/lib/lib.dart';

class BottomNavColumn extends StatelessWidget {
  const BottomNavColumn({
    Key? key,
    required this.icon,
    required this.labelText,
    this.color,
    this.showIcon = true,
    required this.fontWeight,
    required this.onPressed,
  }) : super(key: key);

  final String icon;
  final String labelText;
  final Color? color;
  final bool showIcon;
  final FontWeight fontWeight;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: onPressed,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon)
            SvgPicture.asset(
              icon,
              color: color,
              height: Sizer.height(20),
              width: Sizer.width(20),
            ),
          Si<PERSON><PERSON><PERSON>(height: Sizer.height(6)),
          Container(
            padding: EdgeInsets.only(
              top: !showIcon ? Sizer.height(20) : 0,
            ),
            child: Text(
              labelText,
              style: AppTypography.text12.copyWith(
                color: color ?? AppColors.primaryBlue,
                fontWeight: fontWeight,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
