import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/lib/lib.dart';
import 'package:korrency/ui/components/components.dart';

class BeneficiaryScamSheet extends StatefulWidget {
  const BeneficiaryScamSheet({super.key});

  @override
  State<BeneficiaryScamSheet> createState() => _BeneficiaryScamSheetState();
}

class _BeneficiaryScamSheetState extends State<BeneficiaryScamSheet> {
  bool _hasReadTerms = false;
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      // height: Sizer.screenHeight * 0.50,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(10),
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Icons.close,
                size: Sizer.width(25),
                color: AppColors.gray500,
              ),
            ),
          ),
          const YBox(24),
          SvgPicture.asset(
            AppSvgs.beneficiaryScam,
            height: Sizer.height(154),
          ),
          const YBox(16),
          Text(
            'Avoid scams and fraud, learn how \nto protect your account',
            textAlign: TextAlign.center,
            style: AppTypography.text20.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const YBox(20),
          ScamArrow(
              text:
                  'Be cautious if you’re told your account is at risk and asked to make an unexpected payment'),
          const YBox(16),
          ScamArrow(
              text:
                  'Think twice if the offer sounds too good to be true — it probably is'),
          const YBox(16),
          ScamArrow(
              text:
                  "Always verify the identity of the person you're paying, especially if you only met them online."),
          const YBox(16),
          ScamArrow(
              text:
                  'Take warnings seriously if someone tells you to ignore alerts or messages like this one.'),
          const YBox(40),
          Row(
            children: [
              CustomCheckbox(
                isSelected: _hasReadTerms,
                selectedColor: AppColors.green59,
                onTap: () {
                  _hasReadTerms = !_hasReadTerms;
                  setState(() {});
                },
              ),
              const XBox(16),
              Expanded(
                child: Text(
                  "I have read and understood",
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w400,
                  ),
                ),
              )
            ],
          ),
          const YBox(40),
          CustomBtn.solid(
            onTap: () {
              Navigator.pop(context, _hasReadTerms);
            },
            online: _hasReadTerms,
            height: 44,
            borderRadius: BorderRadius.circular(8),
            text: "Continue",
          ),
          const YBox(30),
        ],
      ),
    );
  }
}

class ScamArrow extends StatelessWidget {
  const ScamArrow({
    super.key,
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SvgPicture.asset(AppSvgs.point),
        const XBox(16),
        Expanded(
          child: Text(
            text,
            style: AppTypography.text14.copyWith(
              color: AppColors.textBlack800,
            ),
          ),
        ),
      ],
    );
  }
}
