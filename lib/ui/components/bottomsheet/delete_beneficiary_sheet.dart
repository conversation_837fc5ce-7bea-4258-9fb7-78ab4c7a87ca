import 'package:korrency/core/lib/lib.dart';
import 'package:korrency/ui/components/components.dart';

class DeleteBeneficiarySheet extends StatelessWidget {
  const DeleteBeneficiarySheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      // height: Sizer.screenHeight * 0.50,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(10),
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Icons.close,
                size: Sizer.width(25),
                color: AppColors.gray500,
              ),
            ),
          ),
          imageHelper(
            AppImages.warning,
            height: Sizer.height(100),
            width: Sizer.width(100),
          ),
          const YBox(10),
          Text(
            'Delete Beneficiary',
            style: AppTypography.text20.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const YBox(4),
          Text(
            'Are you sure you want to continue with this action.?',
            textAlign: TextAlign.center,
            style: AppTypography.text16.copyWith(
              color: AppColors.textBlack800,
            ),
          ),
          const YBox(16),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(12),
              vertical: Sizer.height(8),
            ),
            color: AppColors.blu000,
            child: const UserListTile(
              name: 'Abram Arcand',
            ),
          ),
          const YBox(40),
          CustomBtn.solid(
            onTap: () {
              Navigator.pop(context);
              BsWrapper.bottomSheet(
                context: context,
                widget: const SheetComfirmation(),
              );
            },
            online: true,
            text: "Delete",
          ),
          const YBox(50),
        ],
      ),
    );
  }
}
