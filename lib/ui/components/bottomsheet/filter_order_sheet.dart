import 'package:korrency/core/lib/lib.dart';
import 'package:korrency/ui/components/components.dart';

class FilterOrderSheet extends StatefulWidget {
  const FilterOrderSheet({Key? key}) : super(key: key);

  @override
  State<FilterOrderSheet> createState() => _FilterOrderSheetState();
}

class _FilterOrderSheetState extends State<FilterOrderSheet> {
  GenderType genderType = GenderType.none;
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Choose Order",
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Icon(
                  Icons.close,
                  size: Sizer.radius(23),
                ),
              )
            ],
          ),
          const YBox(25),
          InkWell(
            onTap: () {
              setState(() {});
            },
            child: ContainerWithBluewishBg(
              bgColor: AppColors.gray100,
              padding: EdgeInsets.symmetric(
                vertical: Sizer.height(20),
                horizontal: Sizer.width(16),
              ),
              child: WalletListTile(
                title: "Ascending Order",
                subTitle: "Oldest rates to Newest",
                subTitleStyle: AppTypography.text14.copyWith(
                  color: AppColors.textGray,
                ),
                isSelected: true,
              ),
            ),
          ),
          const YBox(20),
          InkWell(
            onTap: () {
              setState(() {});
            },
            child: ContainerWithBluewishBg(
              bgColor: AppColors.gray100,
              padding: EdgeInsets.symmetric(
                vertical: Sizer.height(20),
                horizontal: Sizer.width(16),
              ),
              child: WalletListTile(
                title: "Descending Order",
                subTitle: "Newest Rates to Oldest",
                subTitleStyle: AppTypography.text14.copyWith(
                  color: AppColors.textGray,
                ),
                isSelected: false,
              ),
            ),
          ),
          const YBox(120),
          CustomBtn.solid(
            onTap: () {
              // Navigator.pushNamed(context, RoutePath.welcomeScreen);
            },
            online: true,
            text: "Apply Filter",
          ),
          const YBox(50),
        ],
      ),
    );
  }
}
