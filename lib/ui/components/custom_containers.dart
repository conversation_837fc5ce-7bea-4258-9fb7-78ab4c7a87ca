import 'package:korrency/core/lib/lib.dart';

class ContainerWithTopBorderRadius extends StatelessWidget {
  const ContainerWithTopBorderRadius({
    super.key,
    required this.child,
    this.height,
    this.padding,
  });

  final Widget child;
  final double? height;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    // print(Sizer.screenWidth);
    // print(Sizer.screenHeight);
    return Container(
      height: height,
      width: Sizer.screenWidth,
      padding: padding ??
          EdgeInsets.symmetric(
            horizontal: Sizer.width(20),
          ),
      decoration: BoxDecoration(
        color: AppColors.bgWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(
            Sizer.width(12),
          ),
          topRight: Radius.circular(
            Sizer.width(12),
          ),
        ),
      ),
      child: child,
    );
  }
}

class ContainerWithBluewishBg extends StatelessWidget {
  const ContainerWithBluewishBg({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.bgColor,
    this.borderCorlor,
    this.padding,
  });

  final Widget child;
  final double? width;
  final double? height;
  final Color? bgColor;
  final Color? borderCorlor;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width ?? Sizer.screenWidth,
      padding: padding ??
          EdgeInsets.symmetric(
            horizontal: Sizer.width(16),
            vertical: Sizer.height(15),
          ),
      decoration: BoxDecoration(
        color: bgColor ?? AppColors.blue100,
        borderRadius: BorderRadius.circular(4),
        border: borderCorlor == null
            ? null
            : Border.all(
                color: borderCorlor ?? AppColors.blue100,
              ),
      ),
      child: child,
    );
  }
}
