import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AnswerQuestionScreen extends StatefulWidget {
  const AnswerQuestionScreen({Key? key}) : super(key: key);

  @override
  State<AnswerQuestionScreen> createState() => _AnswerQuestionScreenState();
}

class _AnswerQuestionScreenState extends State<AnswerQuestionScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SecQuestVM>().getUserSecQuestion();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SecQuestVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: ArrowBack(
                      onTap: () {
                        Navigator.pop(context);
                        vm.answersControllers[SecConst.userAnswer]?.clear();
                      },
                    ),
                  ),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Answer Security Questions",
                    subtitle:
                        "As a last layer of security, answer your security questions",
                  ),
                  const YBox(24),
                  Expanded(
                    child: Container(
                      width: Sizer.screenWidth,
                      color: AppColors.white,
                      child: ListView(
                        padding: EdgeInsets.only(
                          bottom: Sizer.height(24),
                        ),
                        children: [
                          CustomTextField(
                            labelText: "Choose your first question",
                            hintText: 'When were you born',
                            isReadOnly: true,
                            fillColor: AppColors.litGrey100,
                            controller: vm.questionControllers[SecConst.user],
                            showLabelHeader: true,
                            borderRadius: Sizer.height(4),
                          ),
                          const YBox(20),
                          CustomTextField(
                            labelText: "Provide an answer",
                            showLabelHeader: true,
                            borderRadius: Sizer.height(4),
                            isPassword: true,
                            controller:
                                vm.answersControllers[SecConst.userAnswer],
                            onChanged: (_) => vm.reBuildUI(),
                          ),
                        ],
                      ),
                    ),
                  ),
                  CustomBtn.solid(
                    onTap: () {
                      _confirmQuest();
                    },
                    online: vm.answersControllers[SecConst.userAnswer]?.text
                            .isNotEmpty ??
                        false,
                    text: "Continue",
                  ),
                  const YBox(60),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  _confirmQuest() {
    var secQuestVm = context.read<SecQuestVM>();
    secQuestVm.confirmAnswerToSecQuestion().then((value) {
      if (value.success) {
        Navigator.pop(context);
        Navigator.pushNamed(context, RoutePath.setupYourPinScreen);
        secQuestVm.answersControllers[SecConst.userAnswer]?.clear();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }
}
