import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class SecurityQuestionsScreen extends StatefulWidget {
  const SecurityQuestionsScreen({Key? key}) : super(key: key);

  @override
  State<SecurityQuestionsScreen> createState() =>
      _SecurityQuestionsScreenState();
}

class _SecurityQuestionsScreenState extends State<SecurityQuestionsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadQuestions();
    });
  }

  _loadQuestions() {
    if (context.read<SecQuestVM>().secQuestions.isEmpty) {
      context.read<SecQuestVM>().getSecurityQuestions();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SecQuestVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.white,
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ).copyWith(
                  top: Sizer.height(20),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        child: ArrowBack(
                          onTap: () {
                            vm.clearData();
                            Navigator.pop(context);
                          },
                        ),
                      ),
                      const YBox(30),
                      const AuthTextSubTitle(
                        title: "Set Your Security Questions",
                        subtitle:
                            "Add an extra layer of security for important changes",
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Choose your first question",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        showSuffixIcon: true,
                        isReadOnly: true,
                        suffixIcon: Icon(
                          Iconsax.arrow_down_1,
                          color: AppColors.gray500,
                          size: Sizer.height(26),
                        ),
                        controller: vm.questionControllers[SecConst.sec1],
                        onTap: () {
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: const SecurityQuestionSheet(
                              questLevelsType: QuestLevelsType.first,
                            ),
                          );
                        },
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Provide an answer",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        controller: vm.answersControllers[SecConst.sec1Answer],
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Choose your second question",
                        showLabelHeader: true,
                        isReadOnly: true,
                        borderRadius: Sizer.height(4),
                        showSuffixIcon: true,
                        suffixIcon: Icon(
                          Iconsax.arrow_down_1,
                          color: AppColors.gray500,
                          size: Sizer.height(26),
                        ),
                        controller: vm.questionControllers[SecConst.sec2],
                        onTap: () {
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: const SecurityQuestionSheet(
                              questLevelsType: QuestLevelsType.second,
                            ),
                          );
                        },
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Provide an answer",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        controller: vm.answersControllers[SecConst.sec2Answer],
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Choose your third question",
                        showLabelHeader: true,
                        isReadOnly: true,
                        borderRadius: Sizer.height(4),
                        showSuffixIcon: true,
                        suffixIcon: Icon(
                          Iconsax.arrow_down_1,
                          color: AppColors.gray500,
                          size: Sizer.height(26),
                        ),
                        controller: vm.questionControllers[SecConst.sec3],
                        onTap: () {
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: const SecurityQuestionSheet(
                              questLevelsType: QuestLevelsType.third,
                            ),
                          );
                        },
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Provide an answer",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        controller: vm.answersControllers[SecConst.sec3Answer],
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                      const YBox(80),
                      CustomBtn.solid(
                        onTap: () {
                          _setSecQuestions();
                        },
                        online: vm.btnIsActive,
                        text: "Continue",
                      ),
                      const YBox(100)
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _setSecQuestions() {
    context.read<SecQuestVM>().setSecQuestions().then((value) {
      if (value.success) {
        _showSuccessScreen();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _showSuccessScreen() {
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: "Security Question Set\n Successfully",
        btnText: "Continue",
        btnTap: () {
          Navigator.pushReplacementNamed(
              NavigatorKeys.appNavigatorKey.currentContext!,
              RoutePath.dashboardNav);
        },
      ),
    );
  }
}
