// ignore_for_file: use_build_context_synchronously

import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SecurityScreen extends StatefulWidget {
  const SecurityScreen({Key? key}) : super(key: key);

  @override
  State<SecurityScreen> createState() => _SecurityScreenState();
}

class _SecurityScreenState extends State<SecurityScreen> {
  // final bool _isActive = false;
  bool _useFaceId = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      StorageService.getBoolItem(StorageKey.fingerPrintIsEnabled).then((value) {
        setState(() {
          _useFaceId = value ?? false;
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Safe<PERSON>rea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: const ArrowBack(),
              ),
              const YBox(30),
              const AuthTextSubTitle(
                title: "Security",
                subtitle: "All things that covers your profile on the app",
              ),
              const YBox(24),
              Expanded(
                child: Container(
                  width: Sizer.screenWidth,
                  color: AppColors.white,
                  child: ListView(
                    padding: EdgeInsets.only(
                      top: Sizer.height(24),
                      bottom: Sizer.height(24),
                    ),
                    children: [
                      MenuListTile(
                        title: 'Change Transaction PIN',
                        iconData: Iconsax.user_edit,
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.answerQuestionScreen,
                          );
                        },
                      ),
                      const YBox(20),
                      MenuListTile(
                        title: 'Unlock with Biometrics',
                        iconData: Iconsax.finger_cricle,
                        trailingWidget: CustomSwitch(
                          value: _useFaceId,
                          onChanged: updateFingerPrint,
                        ),
                        onPressed: () {
                          // BsWrapper.bottomSheet(
                          //     context: context,
                          //     widget: const EnableFingerprintSheet());
                        },
                      ),
                      const YBox(20),
                      MenuListTile(
                        title: 'Change Password',
                        iconData: Iconsax.lock_1,
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.newPasswordScreen,
                          );
                        },
                      ),
                      const YBox(20),
                      MenuListTile(
                        title: 'Trusted Device',
                        useImageIcon: true,
                        isSvg: true,
                        imageIcon: AppSvgs.phone1,
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.myDeviceScreen,
                          );
                        },
                      ),
                      const YBox(20),
                      MenuListTile(
                        title: '2FA',
                        useImageIcon: true,
                        isSvg: true,
                        imageIcon: AppSvgs.ffa,
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.twoFactorAuthScreen,
                          );
                        },
                      ),
                      // const YBox(20),
                      // MenuListTile(
                      //   title: 'Security Question',
                      //   useImageIcon: true,
                      //   isSvg: true,
                      //   imageIcon: AppSvgs.ffa,
                      //   onPressed: () {
                      //     Navigator.pushNamed(
                      //       context,
                      //       RoutePath.securityQuestionScreen,
                      //     );
                      //   },
                      // ),
                      // const YBox(20),
                      // MenuListTile(
                      //   title: 'Create Transaction PIN',
                      //   useImageIcon: true,
                      //   isSvg: true,
                      //   imageIcon: AppSvgs.ffa,
                      //   onPressed: () {
                      //     BsWrapper.bottomSheet(
                      //         context: context, widget: const SetupPinScreen());
                      //   },
                      // ),
                      // const YBox(20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  updateFingerPrint(bool val) async {
    if (val) {
      bool isAuthenticated = await BiometricService.authenticate();
      if (isAuthenticated) {
        StorageService.storeBoolItem(StorageKey.fingerPrintIsEnabled, val);
        setState(() {
          _useFaceId = val;
        });
      } else {
        FlushBarToast.fLSnackBar(
          message: "Biometric Authentication Failed",
        );
      }
    } else {
      await StorageService.removeBoolItem(StorageKey.fingerPrintIsEnabled);
      setState(() {
        _useFaceId = val;
      });
    }
  }
}
