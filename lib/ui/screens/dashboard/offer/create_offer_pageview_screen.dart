import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/lib/lib.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/offer/pageview/pageview.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class CreateOfferPageviewScreen extends StatefulWidget {
  const CreateOfferPageviewScreen({
    Key? key,
    required this.args,
  }) : super(key: key);

  final OfferTypeArg args;

  @override
  State<CreateOfferPageviewScreen> createState() =>
      _CreateOfferPageviewScreenState();
}

class _CreateOfferPageviewScreenState extends State<CreateOfferPageviewScreen> {
  final PageController _pageController = PageController();

  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: SafeArea(
        bottom: false,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ArrowBack(
                    onTap: () {
                      if (_currentPage == 0) {
                        return Navigator.pop(context);
                      }
                      _previousPage();
                    },
                  ),
                  Row(
                    children: [
                      Text(
                        "How It Works",
                        style: AppTypography.text12.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.textBlack1000,
                        ),
                      ),
                      const XBox(6),
                      const Icon(
                        Iconsax.info_circle,
                        size: 20,
                        color: AppColors.textBlack1000,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const YBox(36),
            LinearPercentIndicator(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              animation: true,
              animationDuration: 2000,
              lineHeight: 4.0,

              percent: _currentPage == 0 ? 0.5 : 1.0,
              backgroundColor: AppColors.baseGray,
              progressColor: AppColors.primaryBlue,
              animateFromLastPercent:
                  true, // Ensures animation starts from the last known percentage
              restartAnimation:
                  false, // Prevents animation from restarting every time the percentage changes
            ),
            const YBox(16),
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (value) => setState(() {
                  _currentPage = value;
                }),
                children: [
                  CreateBuyOfferPageview(
                    offerType: widget.args.offerType,
                    onTap: () {
                      _nextPage();
                    },
                  ),
                  MinimumExchangePageView(offerType: widget.args.offerType),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  _previousPage() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }
}
