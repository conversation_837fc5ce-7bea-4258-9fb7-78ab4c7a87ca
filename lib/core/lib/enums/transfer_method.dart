enum TransferMethodType {
  korrency,
  interac,
  bankTransfer,
  mobileMoney,
}

extension TransferMethodTypeExtension on TransferMethodType {
  String get text {
    switch (this) {
      case TransferMethodType.korrency:
        return "Korrency";
      case TransferMethodType.interac:
        return "Interac e-Transfer";
      case TransferMethodType.bankTransfer:
        return "Bank Transfer";
      case TransferMethodType.mobileMoney:
        return "Mobile Money";
    }
  }
}
