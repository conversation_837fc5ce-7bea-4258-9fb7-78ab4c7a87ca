import 'package:korrency/core/lib/lib.dart';

class AppTypography {
  static TextStyle text8 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(8),
  );

  static TextStyle text10 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(10),
  );

  static TextStyle text11 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(11),
    letterSpacing: 0.1,
  );

  static TextStyle text12 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(12),
    letterSpacing: 0.1,
  );

  static TextStyle text12b = TextStyle(
    fontWeight: FontWeight.bold,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(12),
    letterSpacing: 0.1,
  );

  static TextStyle text13 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(13),
  );

  static TextStyle text14 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(14),
  );

  static TextStyle text14b = TextStyle(
    fontWeight: FontWeight.bold,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(14),
  );
  static TextStyle text15 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(15),
  );

  static TextStyle text16 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(16),
  );

  static TextStyle text16b = TextStyle(
    fontWeight: FontWeight.bold,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(16),
  );

  static TextStyle text18 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(18),
  );

  static TextStyle text20 = TextStyle(
    color: AppColors.baseBlack,
    fontSize: Sizer.text(20),
  );
  static TextStyle text20b = TextStyle(
    fontWeight: FontWeight.bold,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(20),
  );

  static TextStyle text24 = TextStyle(
    color: AppColors.baseBlack,
    fontSize: Sizer.text(24),
  );

  static TextStyle text28 = TextStyle(
    color: AppColors.baseBlack,
    fontSize: Sizer.text(28),
  );

  static TextStyle text32 = TextStyle(
    color: AppColors.baseBlack,
    height: 1.2,
    fontSize: Sizer.text(32),
  );

  static TextStyle text36 = TextStyle(
    color: AppColors.baseBlack,
    height: 1.2,
    fontSize: Sizer.text(36),
  );
}
