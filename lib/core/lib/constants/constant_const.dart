import 'package:korrency/core/core.dart';

class OfferDetailArg {
  final int id;

  OfferDetailArg(this.id);
}

class OfferConst {
  // Offers
  static const String sellOffer = "sell offer";
  static const String buyOffer = "buy offer";
  static const String addition = "addition";
  static const String multiply = "multiply";
}

class FeesTypes {
  static const String transfer = "transfer";
  static const String buyOffer = "buy_offer";
  static const String sellOffer = "sell_offer";
}

class TransferMethod {
  static const String bankTransfer = "bank_transfer";
  static const String mobileMoney = "mobile_money";
  static const String korrency = "korrency";
  static const String interac = "interac";
}

class UserStatusConst {
  static const String processing = 'processing';
  static const String pending = 'pending';
  static const String verified = 'verified';
}

class MarketOffer {
  static const String active = 'active';
  static const String completed = 'completed';
  static const String expired = 'expired';
}

const String mail = 'mail';
const String sms = 'sms';

String referalShare(
    {String? refBonusAmt,
    String? refamtForReferral,
    String? refCode,
    String? refLink}) {
  return "Hey Friend, \nI've been using Korrency for my money transfers, and it's awesome! \n\nUse my referral code to sign up. Make your first transaction of ${AppUtils.dollarSymbolWithSpace}$refamtForReferral or more, and we’ll both get ${AppUtils.dollarSymbolWithSpace}$refBonusAmt free! \n \nReferral Code: $refCode \n \nJoin now and experience great rates with zero fees. \n \n$refLink";
}

String korrencyUserShare({String? userName, String? refCode}) {
  return "Hey Friend! \nYou can send money to me on Korrency using my username \n\n$userName. \n\nIt's quick, easy, and secure! \n\nhttps://korrency.onelink.me/9BIc?referrer=$refCode";
}

class GlobalVar {
  static String? activeRoute;
}

// Ios push notifcation key
// Name:flutter app
// Key ID:N6ZWYW9644
// Services:Apple Push Notifications service (APNs)
// Team Id: 7N39U56X3Y
