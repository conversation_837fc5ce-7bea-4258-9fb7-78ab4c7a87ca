import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/auth/confirmation_body.dart';
import 'package:korrency/ui/screens/screens.dart';

class AppRouters {
  static Route<dynamic> getRoute(RouteSettings settings) {
    // GlobalVar.activeRoute = settings.name;

    switch (settings.name) {
      case RoutePath.welcomeBackScreen:
        bool fromSplash =
            settings.arguments is bool ? settings.arguments as bool : true;
        return MaterialPageRoute(
            builder: (_) => WelcomeBackScreen(fromSplash: fromSplash));

      case RoutePath.splashScreen:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      case RoutePath.introScreen:
        return MaterialPageRoute(builder: (_) => const IntroScreen());
      case RoutePath.updateAvailableScreen:
        return MaterialPageRoute(builder: (_) => const UpdateAvailableScreen());

      case RoutePath.createFreshDeskTicketWebview:
        final arg = settings.arguments;
        if (arg is WebViewArg) {
          return MaterialPageRoute(
            builder: (_) => CreateFreshdeskTicketWebview(arg: arg),
          );
        }
        return errorScreen('Invalid arguments');

      case RoutePath.createAcctScreen:
        return MaterialPageRoute(builder: (_) => const CreateAccountScreen());
      case RoutePath.verifyPhoneNumScreen:
        return MaterialPageRoute(
            builder: (_) => const VerifyPhoneNumberScreen());
      case RoutePath.emailAndPasswordScreen:
        return MaterialPageRoute(
            builder: (_) => const EmailAndPasswordScreen());
      case RoutePath.verifyEmailScreen:
        return MaterialPageRoute(builder: (_) => const VerifyEmailAddress());
      case RoutePath.biometricScreen:
        return MaterialPageRoute(builder: (_) => const BiometricScreen());

      case RoutePath.frequentRecipientCountryScreen:
        final arg =
            settings.arguments is bool ? settings.arguments as bool : false;
        return MaterialPageRoute(
            builder: (_) => FrequentRecipientCountryScreen(fromHome: arg));

      case RoutePath.successScreen:
        final args = settings.arguments;
        if (args is ConfirmationArg) {
          return MaterialPageRoute(
            builder: (_) => SuccessScreen(
              arg: args,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      case RoutePath.successConfirmScreen:
        final args = settings.arguments;
        if (args is SuccessConfirmArg) {
          return MaterialPageRoute(
            builder: (_) => SuccessConfirmScreen(arg: args),
          );
        }
        return errorScreen('Invalid arguments');

      // Login flow
      case RoutePath.loginScreen:
        return MaterialPageRoute(builder: (_) => const LoginScreen());
      case RoutePath.forgotPasswordScreen:
        return MaterialPageRoute(builder: (_) => const ForgotPasswordScreen());
      case RoutePath.createNewPasswordScreen:
        return MaterialPageRoute(
            builder: (_) => const CreateNewPasswordScreen());
      case RoutePath.passwordResetScreen:
        return MaterialPageRoute(builder: (_) => const PasswordResetScreen());

      case RoutePath.notifcationScreen:
        return MaterialPageRoute(builder: (_) => const NotificationScreen());
      case RoutePath.preferredMehodScreen:
        return MaterialPageRoute(builder: (_) => const PreferredMethodScreen());
      case RoutePath.interacTransferScreen:
        return MaterialPageRoute(builder: (_) => const InteracTransferScreen());
      case RoutePath.anotherInteracEmailScreen:
        return MaterialPageRoute(
            builder: (_) => const AnotherInteracEmailScreen());
      case RoutePath.verifyInteracEmailScreen:
        final args = settings.arguments;
        if (args is String) {
          return MaterialPageRoute(
              builder: (_) => VerifyInteracEmailScreen(email: args));
        }
        return errorScreen('Invalid arguments');
      case RoutePath.interacEmailConfirmationScreen:
        final arg = settings.arguments;
        if (arg is StatementConfirmScreenArgs) {
          return MaterialPageRoute(
              builder: (_) => StatementConfirmationScreen(args: arg));
        }
        return errorScreen('Invalid arguments');
      case RoutePath.accountDetailsScreen:
        return MaterialPageRoute(builder: (_) => const AccountDetailsScreen());
      case RoutePath.accountLimitScreen:
        return MaterialPageRoute(builder: (_) => const AccounntLimitScreen());
      case RoutePath.shareByUsernameScreen:
        return MaterialPageRoute(builder: (_) => const ShareByUsernameScreen());
      case RoutePath.accountStatementScreen:
        return MaterialPageRoute(
            builder: (_) => const AccountStatementScreen());

      case RoutePath.convertCurrencyScreen:
        return MaterialPageRoute(builder: (_) => const ConvertCurrencyScreen());

      case RoutePath.sendMoneyScreen:
        final args = settings.arguments as SendMoneyArg?;
        return MaterialPageRoute(builder: (_) => SendMoneyScreen(arg: args));

      case RoutePath.sendToNgnScreen:
        if (settings.arguments is PaymentMethod) {
          return MaterialPageRoute(
            builder: (_) => SendBankTransferScreen(
              paymentMethod: settings.arguments as PaymentMethod,
            ),
          );
        }
        return errorScreen('Invalid arguments');

      case RoutePath.sendToCadScreen:
        return MaterialPageRoute(builder: (_) => const SendToCadScreen());
      case RoutePath.reviewScreen:
        var arg =
            settings.arguments is bool ? settings.arguments as bool : false;
        return MaterialPageRoute(
            builder: (_) => SendMoneyReviewScreen(
                  isKorrencyUser: arg,
                ));
      case RoutePath.sendMoneyMethodScreen:
        return MaterialPageRoute(builder: (_) => const SendMoneyMethodScreen());
      case RoutePath.sendMoneyAuthorizeScreen:
        return MaterialPageRoute(
            builder: (_) => const SendMoneyAuthorizeScreen());

      case RoutePath.sendMoneyMobileMoney:
        if (settings.arguments is PaymentMethod) {
          return MaterialPageRoute(
            builder: (_) => SendMoneyMobileMoney(
              paymentMethod: settings.arguments as PaymentMethod,
            ),
          );
        }
        return errorScreen('Invalid arguments');

      case RoutePath.authorizeTransaction:
        if (settings.arguments is OfferTypeArg) {
          return MaterialPageRoute(
            builder: (_) => AuthorizeTransactionScreen(
              args: settings.arguments as OfferTypeArg,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      case RoutePath.referralScreen:
        return MaterialPageRoute(builder: (_) => const ReferralScreen());
      case RoutePath.exchangeRateScreen:
        return MaterialPageRoute(builder: (_) => const ExchangeRateScreen());
      case RoutePath.navigateExchangeOffers:
        return MaterialPageRoute(
            builder: (_) => const NavigateExchangeOffers());
      case RoutePath.sendToKorrencyUser:
        return MaterialPageRoute(builder: (_) => const KorrencyUserScreen());

      case RoutePath.transactionScreen:
        return MaterialPageRoute(builder: (_) => const TransactionsScreen());
      case RoutePath.transactionStatusScreen:
        final args = settings.arguments;
        if (args is ConfirmationArg) {
          return MaterialPageRoute(
            builder: (_) => TransactionStatusScreen(
              arg: args,
            ),
          );
        }
        return errorScreen('Invalid arguments');

      case RoutePath.transactionDetailsScreen:
        if (settings.arguments is TransactionArg) {
          return MaterialPageRoute(
              builder: (_) => TransactionDetailsScreen(
                    transactionArg: settings.arguments as TransactionArg,
                  ));
        }
        return errorScreen('Invalid arguments');

      case RoutePath.securityScreen:
        return MaterialPageRoute(builder: (_) => const SecurityScreen());
      case RoutePath.answerQuestionScreen:
        return MaterialPageRoute(builder: (_) => const AnswerQuestionScreen());
      case RoutePath.confirmYourPinScreen:
        return MaterialPageRoute(builder: (_) => const ConfirmYourPinScreen());
      case RoutePath.setupYourPinScreen:
        return MaterialPageRoute(builder: (_) => const SetupYourPinScreen());
      case RoutePath.newPasswordScreen:
        return MaterialPageRoute(builder: (_) => const NewPasswordScreen());
      case RoutePath.changePasswordScreen:
        return MaterialPageRoute(builder: (_) => const ChangePasswordScreen());

      case RoutePath.myDeviceScreen:
        return MaterialPageRoute(builder: (_) => const MyDevicesScreen());
      case RoutePath.verifyTrustedDeviceScreen:
        return MaterialPageRoute(
            builder: (_) => const VerifyTrustedDevicePinScreen());

      case RoutePath.twoFactorAuthScreen:
        return MaterialPageRoute(builder: (_) => const TwoFactorAuthScreen());
      case RoutePath.securityQuestionScreen:
        return MaterialPageRoute(
            builder: (_) => const SecurityQuestionsScreen());

      case RoutePath.aboutUsScreen:
        return MaterialPageRoute(builder: (_) => const AboutUsScreen());

      case RoutePath.preferenceScreen:
        return MaterialPageRoute(builder: (_) => const PreferenceScreen());

      case RoutePath.helpSupportScreen:
        return MaterialPageRoute(builder: (_) => const HelpSupportScreen());
      case RoutePath.helpVideoScreen:
        return MaterialPageRoute(builder: (_) => const HelpVideoScreen());
      case RoutePath.reactOutToUsScreen:
        return MaterialPageRoute(builder: (_) => const ReachOutToUsScreen());
      case RoutePath.videoPlayerScreen:
        if (settings.arguments is VideoPlayerArg) {
          return MaterialPageRoute(
            builder: (_) => VideoPlayerScreen(
              args: settings.arguments as VideoPlayerArg,
            ),
          );
        }
        return errorScreen('Invalid arguments');

      case RoutePath.infoBeneficiaryScreen:
        return MaterialPageRoute(builder: (_) => const InfoBeneficiaryScreen());
      case RoutePath.selectCountryScreen:
        return MaterialPageRoute(builder: (_) => const SelectCountryScreen());
      case RoutePath.bankTransferScreen:
        if (settings.arguments is BeneficiaryPaymentArg) {
          return MaterialPageRoute(
            builder: (_) => BankTransferScreen(
              arg: settings.arguments as BeneficiaryPaymentArg,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      // return MaterialPageRoute(builder: (_) => const BankTransferScreen());
      case RoutePath.mobileMoneyScreen:
        if (settings.arguments is BeneficiaryPaymentArg) {
          return MaterialPageRoute(
            builder: (_) => MobileMoneyScreen(
              arg: settings.arguments as BeneficiaryPaymentArg,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      // return MaterialPageRoute(builder: (_) => const MobileMoneyScreen());
      case RoutePath.newBeneficiaryScreen:
        if (settings.arguments is Currency) {
          return MaterialPageRoute(
            builder: (_) => NewBeneficiaryScreen(
              currency: settings.arguments as Currency,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      case RoutePath.beneficiaryScreen:
        var arg = settings.arguments as BeneficiaryArg?;
        return MaterialPageRoute(builder: (_) => BeneficiaryScreen(arg: arg));
      case RoutePath.beneficiaryPreferredPaymentScreen:
        if (settings.arguments is Currency) {
          return MaterialPageRoute(
            builder: (_) => BeneficiaryPreferredMethodScreen(
              currency: settings.arguments as Currency,
            ),
          );
        }
        return errorScreen('Invalid arguments');

      case RoutePath.profileDetailsScreen:
        return MaterialPageRoute(builder: (_) => const ProfileDetailsScreen());
      case RoutePath.profileScreen:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());
      case RoutePath.selectAvatarScreen:
        return MaterialPageRoute(builder: (_) => const SelectAvatarScreen());
      case RoutePath.previewAvatarScreen:
        if (settings.arguments is String) {
          return MaterialPageRoute(
            builder: (_) => PreviewAvatarScreen(
              avatarString: settings.arguments as String,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      case RoutePath.deactivateAccountScreen:
        return MaterialPageRoute(
            builder: (_) => const DeactivateAccountScreen());
      case RoutePath.deactivateAccountReasonScreen:
        return MaterialPageRoute(
            builder: (_) => const DeactivateAccountReasonScreen());

      case RoutePath.howOfferWorks:
        return MaterialPageRoute(builder: (_) => const HowItWorksScreen());
      case RoutePath.createOfferScreen:
        return MaterialPageRoute(builder: (_) => const CreateOfferScreen());
      case RoutePath.createBuyOfferScreen:
        if (settings.arguments is OfferTypeArg) {
          return MaterialPageRoute(
            builder: (_) => CreateOfferPageviewScreen(
              args: settings.arguments as OfferTypeArg,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      case RoutePath.reviewOfferScreen:
        if (settings.arguments is OfferTypeArg) {
          return MaterialPageRoute(
            builder: (_) => ReviewOfferScreen(
              args: settings.arguments as OfferTypeArg,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      case RoutePath.offerTransactionStatusScreen:
        return MaterialPageRoute(
            builder: (_) => const OfferTransactionStatusScreen());

      case RoutePath.offerDetailsScreen:
        if (settings.arguments is OfferDetailArg) {
          return MaterialPageRoute(
            builder: (_) => OfferDetailsScreen(
              arg: settings.arguments as OfferDetailArg,
            ),
          );
        }
        return errorScreen('Invalid arguments');

      case RoutePath.myOfferDetailsScreen:
        if (settings.arguments is OfferDetailArg) {
          return MaterialPageRoute(
            builder: (_) => MyOfferDetailsScreen(
              arg: settings.arguments as OfferDetailArg,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      case RoutePath.p2ptransferScreen:
        if (settings.arguments is P2pTransferArg) {
          return MaterialPageRoute(
            builder: (_) => P2PTransferScreen(
              arg: settings.arguments as P2pTransferArg,
            ),
          );
        }
        return errorScreen('Invalid arguments');
      case RoutePath.p2pReviewScreen:
        return MaterialPageRoute(builder: (_) => const P2PReviewScreen());
      case RoutePath.p2pPinAuthorizeScreen:
        return MaterialPageRoute(builder: (_) => const P2pPinAuthorizeScreen());
      case RoutePath.setRateAlertScreen:
        return MaterialPageRoute(builder: (_) => const SetRateAlertScreen());
      case RoutePath.setAlertScreen:
        return MaterialPageRoute(builder: (_) => const SetAlertScreen());
      case RoutePath.createAlertScreen:
        return MaterialPageRoute(builder: (_) => const CreateAlertScreen());
      case RoutePath.sortOffersScreen:
        return MaterialPageRoute(builder: (_) => const SortOffersScreen());
      case RoutePath.yourOfferScreen:
        return MaterialPageRoute(builder: (_) => const YourOfferScreen());
      case RoutePath.marketPlaceScreen:
        return MaterialPageRoute(builder: (_) => const MarketPlaceScreen());

      case RoutePath.dashboardNav:
        int index = settings.arguments is int ? settings.arguments as int : 0;
        return MaterialPageRoute(builder: (_) => DashboardNav(index: index));

      default:
        return errorScreen('No route defined for ${settings.name}');
    }
  }

  static MaterialPageRoute errorScreen(String msg) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body: Center(
          child: Text(msg),
        ),
      ),
    );
  }
}
