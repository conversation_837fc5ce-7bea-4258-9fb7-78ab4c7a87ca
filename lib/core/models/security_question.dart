import 'dart:convert';

List<SecurityQuestion> securityQuestionFromJson(String str) =>
    List<SecurityQuestion>.from(
        json.decode(str).map((x) => SecurityQuestion.fromJson(x)));

String securityQuestionToJson(List<SecurityQuestion> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SecurityQuestion {
  int? id;
  String? question;

  SecurityQuestion({
    this.id,
    this.question,
  });

  factory SecurityQuestion.fromJson(Map<String, dynamic> json) =>
      SecurityQuestion(
        id: json["id"],
        question: json["question"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "question": question,
      };
}
