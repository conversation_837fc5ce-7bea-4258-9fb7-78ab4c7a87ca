import 'package:korrency/core/core.dart';

class OnBoardVM extends BaseVM {
  // Declare TextEditingController variables
  TextEditingController phoneOtp = TextEditingController();
  TextEditingController emailOtp = TextEditingController();
  TextEditingController phoneNumC = TextEditingController();
  TextEditingController userNameC = TextEditingController();
  TextEditingController emailC = TextEditingController();
  TextEditingController passwordC = TextEditingController();
  TextEditingController referralC = TextEditingController();
  Country? _country = countries.isNotEmpty ? countries[0] : null;
  Country? get country => _country;

  bool? _acceptedTermsAndConditions;
  bool get acceptedTermsAndConditions => _acceptedTermsAndConditions ?? false;
  bool? _optedForProductUpdate;
  bool get optedForProductUpdate => _optedForProductUpdate ?? false;

  ValidatorStatus _validatorStatus = ValidatorStatus();
  ValidatorStatus get validatorStatus => _validatorStatus;

  bool _isMatchingPassword = false;
  bool get isMatchingPassword => _isMatchingPassword;

  bool _isValidEmail = false;
  bool get isValidEmail => _isValidEmail;

  bool _userNameValid = false;
  bool get userNameValid => _userNameValid;

  void setTemAndCondition(bool value) {
    _acceptedTermsAndConditions = value;
    reBuildUI();
  }

  void setCounctry(Country value) {
    _country = value;
    reBuildUI();
  }

  setOptedForProduct(bool value) {
    _optedForProductUpdate = value;
    reBuildUI();
  }

  validatePassword(String password) {
    _validatorStatus = PasswordValidatorService().validate(password);
    reBuildUI();
  }

  passwordMatch(String password, String confirmPassword) {
    _isMatchingPassword =
        PasswordValidatorService().passwordMatch(password, confirmPassword);
    reBuildUI();
  }

  // phoneNumIsValid(){
  //   _validatorStatus = PhoneValidatorService().validate(phoneNumC.text);
  //   notify();
  // }

  isUserNameValid() {
    _userNameValid = userNameC.text.isNotEmpty;
    reBuildUI();
  }

  emailIsValid() {
    _isValidEmail = emailC.text.isNotEmpty &&
        emailC.text.contains("@") &&
        emailC.text.contains(".") &&
        emailC.text.split('.').last.isNotEmpty;

    reBuildUI();
  }

  signupBtnIsValid() {
    return isValidEmail && userNameValid && validatorStatus.isValid;
  }

  Future<ApiResponse> requestOtp({OtpType otpType = OtpType.phone}) async {
    var body = {
      "recipient": otpType == OtpType.phone
          ? "${_country?.dialCode}${phoneNumC.text.trim()}"
          : emailC.text.trim(),
      "user_name": userNameC.text.trim(), // nullable for phone type
      "referral_code": otpType == OtpType.email ? referralC.text.trim() : null,
      "verification_type":
          otpType == OtpType.phone ? "phone_verification" : "email_verification"
    };
    body.removeWhere((k, v) => v == null || v.isEmpty);
    printty(body, level: "requestOtp");
    return await performApiCall(
      url: "/auth/identity-verification/request-otp",
      method: apiService.post,
      body: body,
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: apiResponse.data,
          message: apiResponse.message,
        );
      },
    );
  }

  Future<ApiResponse> verifyOtp({OtpType otpType = OtpType.phone}) async {
    var body = {
      "recipient": otpType == OtpType.phone
          ? "${_country?.dialCode}${phoneNumC.text.trim()}"
          : emailC.text.trim(),
      "verification_type": otpType == OtpType.phone
          ? "phone_verification"
          : "email_verification",
      "code": otpType == OtpType.phone
          ? phoneOtp.text.trim()
          : emailOtp.text.trim(),
    };
    printty(body, level: "verifyOtp");
    return await performApiCall(
      url: "/auth/identity-verification/verify-otp",
      method: apiService.post,
      body: body,
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: apiResponse.data,
          message: apiResponse.message,
        );
      },
    );
  }

  Future<ApiResponse> signUp() async {
    var body = {
      "phone": "${_country?.dialCode}${phoneNumC.text.trim()}",
      "user_name": userNameC.text.trim(),
      "email": emailC.text.trim(),
      "password": passwordC.text.trim(),
      "referral_code": referralC.text.trim(),
      "country": _country?.name,
      "code": emailOtp.text.trim(),
      "accepted_terms_and_conditions":
          (_acceptedTermsAndConditions ?? false) ? 1 : 0,
      "opted_for_product_update": (_optedForProductUpdate ?? false) ? 1 : 0,
    };
    printty(body, level: "Sign call");
    return await performApiCall(
      url: "/auth/register",
      method: apiService.post,
      body: body,
      onSuccess: (data) {
        String token = data["data"]["token"];
        StorageService.storeAccessToken(token);
        return ApiResponse(
          success: true,
          data: apiResponse.data,
          message: apiResponse.message,
        );
      },
    );
  }

  String? extractDeepLinkValue(String url) {
    try {
      printty('Processing deep link URL: $url');

      // First try standard URI parsing
      try {
        final uri = Uri.parse(url);
        final referrerParam = uri.queryParameters['referrer'];

        if (referrerParam != null && referrerParam.isNotEmpty) {
          printty('Extracted deep link value using Uri.parse: $referrerParam');
          StorageService.storeString(StorageKey.deepLinkValue, referrerParam);
          referralC.text = referrerParam;
          return referrerParam;
        } else {
          printty('No referrer parameter found using Uri.parse');
        }
      } catch (uriError) {
        printty('Error parsing URL with Uri.parse: $uriError');
      }

      // Fallback to manual parsing if Uri.parse fails
      final parts = url.split('?');
      if (parts.length < 2) {
        printty('No query parameters found in URL');
        return '';
      }

      // Get the query parameters part
      final queryParams = parts[1];
      final paramPairs = queryParams.split('&');

      // Look for the referrer parameter
      for (final pair in paramPairs) {
        final keyValue = pair.split('=');
        if (keyValue.length == 2 && keyValue[0] == 'referrer') {
          final referrerValue = keyValue[1];
          printty(
              'Extracted deep link value using manual parsing: $referrerValue');
          StorageService.storeString(StorageKey.deepLinkValue, referrerValue);
          referralC.text = referrerValue;
          return referrerValue;
        }
      }

      // If we get here, we couldn't find the referrer parameter
      printty('No referrer parameter found in URL after manual parsing');
      return '';
    } catch (e) {
      printty('Error extracting deep link value: $e');
    }

    return null;
  }

  getDeepLinkValueFromStorage() async {
    final deepLinkValue =
        await StorageService.getStringItem(StorageKey.deepLinkValue);
    if (deepLinkValue != null) {
      referralC.text = deepLinkValue;
    }

    reBuildUI();
  }

  removeDeepLinkValueFromStorage() async {
    await StorageService.removeStringItem(StorageKey.deepLinkValue);
    referralC.clear();
    reBuildUI();
  }

  clearEmailPasswordCredentials() {
    userNameC.clear();
    emailC.clear();
    passwordC.clear();
    referralC.clear();
    emailOtp.clear();
    _validatorStatus = ValidatorStatus();
    reBuildUI();
  }

  clearData() {
    clearEmailPasswordCredentials();
    phoneNumC.clear();
    phoneOtp.clear();
    _validatorStatus = ValidatorStatus();
    _isMatchingPassword = false;
    _isValidEmail = false;
    _userNameValid = false;
    _acceptedTermsAndConditions = null;
    _optedForProductUpdate = null;

    reBuildUI();
  }

  // Dispose method to dispose of the controllers when no longer needed
  @override
  void dispose() {
    printty('OnBoardVM Disposed');

    _country = null;

    phoneNumC.dispose();
    userNameC.dispose();
    emailC.dispose();
    passwordC.dispose();
    referralC.dispose();

    super.dispose();
  }
}
