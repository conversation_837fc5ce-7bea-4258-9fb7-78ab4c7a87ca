import 'package:korrency/core/core.dart';
import 'package:korrency/env/env_config.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';

class MixpanelService {
  static final MixpanelService _instance = MixpanelService._internal();
  static Mixpanel? _mixpanel;
  final String _token;
  bool _initialized = false;

  // Private constructor, get token from dot env
  MixpanelService._internal() : _token = EnvConfig.mixpanelToken;

  // Factory constructor to return the same instance each time
  factory MixpanelService() {
    return _instance;
  }

  // Initialize Mixpanel
  Future<void> init() async {
    if (!_initialized) {
      try {
        _mixpanel = await Mixpanel.init(
          _token,
          trackAutomaticEvents: true,
          optOutTrackingDefault: false,
        );
        _initialized = true;
        printty('Mixpanel initialized successfully');
      } catch (e) {
        printty('Error initializing Mixpanel: $e');
      }
    }
  }

  // Check if initialized and get the Mixpanel instance
  Mixpanel? get instance {
    if (!_initialized) {
      printty('Mixpanel not initialized. Call init() first.');
      return null;
    }
    return _mixpanel;
  }

  // Track an event
  void track(String eventName, {Map<String, dynamic>? properties}) {
    final mixpanel = instance;
    if (mixpanel != null) {
      mixpanel.track(eventName, properties: properties);
      printty('Tracked event: $eventName with properties: $properties');
    }
  }

  // Identify a user
  void identify(String userId) {
    final mixpanel = instance;
    if (mixpanel != null) {
      mixpanel.identify(userId);
      printty('Identified user: $userId');
    }
  }

  // Set user profile properties
  void setUserProperties(Map<String, dynamic> properties) {
    final mixpanel = instance;
    if (mixpanel != null) {
      for (final entry in properties.entries) {
        mixpanel.getPeople().set(entry.key, entry.value);
      }
      printty('Set user properties: $properties');
    }
  }

  // Register super properties that will be included with all events
  void registerSuperProperties(Map<String, dynamic> properties) {
    final mixpanel = instance;
    if (mixpanel != null) {
      mixpanel.registerSuperProperties(properties);
      printty('Registered super properties: $properties');
    }
  }

  // Track a screen view
  void trackScreen(String screenName,
      {Map<String, dynamic>? additionalProperties}) {
    final properties = <String, dynamic>{
      'screen': screenName,
    };

    if (additionalProperties != null) {
      properties.addAll(additionalProperties);
    }

    track('$screenName View', properties: properties);
  }

  // Flush events
  void flush() {
    final mixpanel = instance;
    if (mixpanel != null) {
      mixpanel.flush();
      printty('Flushed events to Mixpanel');
    }
  }

  // Opt out of tracking
  void optOutTracking() {
    final mixpanel = instance;
    if (mixpanel != null) {
      mixpanel.optOutTracking();
      printty('Opted out of tracking');
    }
  }

  // Opt in to tracking
  void optInTracking() {
    final mixpanel = instance;
    if (mixpanel != null) {
      mixpanel.optInTracking();
      printty('Opted in to tracking');
    }
  }
}
