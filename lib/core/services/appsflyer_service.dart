import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:korrency/core/core.dart';

class AppsflyService {
  static late AppsflyerSdk appsflyerSdk;

  static init() async {
    printty("Initializing AppsFlyer SDK...");

    // Get the AppsFlyer dev key and app ID from environment variables
    final afDevKey = dotenv.env["AF_DEV_KEY"];
    final appId = dotenv.env["AF_APP_ID"];

    printty("AF_DEV_KEY: ${afDevKey != null ? 'Found' : 'Missing'}");
    printty("AF_APP_ID: ${appId != null ? 'Found' : 'Missing'}");

    AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
      afDevKey: afDevKey!,
      appId: appId!,
      showDebug: true,
      timeToWaitForATTUserAuthorization: 15, // for iOS 14.5
      appInviteOneLink: "9BIc", // OneLink ID from your referral link
      disableAdvertisingIdentifier: false,
      disableCollectASA: false,
    );

    appsflyerSdk = AppsflyerSdk(appsFlyerOptions);

    appsflyerSdk.onInstallConversionData((res) {
      printty("onInstallConversionData: $res");
    });

    appsflyerSdk.onAppOpenAttribution((res) {
      printty("onAppOpenAttribution: $res");
    });

    appsflyerSdk.onDeepLinking((DeepLinkResult dp) {
      printty(
          "AppsFlyer deep linking callback triggered with status: ${dp.status}");

      switch (dp.status) {
        case Status.FOUND:
          printty("Deep link FOUND - Full DeepLinkResult: $dp");
          printty("Deep link clickEvent: ${dp.deepLink?.clickEvent}");

          if (dp.deepLink?.clickEvent != null &&
              dp.deepLink?.clickEvent["link"] != null) {
            final link = dp.deepLink?.clickEvent["link"];
            printty("Deep link URL: $link");

            try {
              if (NavigatorKeys.appNavigatorKey.currentContext != null) {
                printty("Context available, extracting deep link value");
                NavigatorKeys.appNavigatorKey.currentContext!
                    .read<OnBoardVM>()
                    .extractDeepLinkValue(link);
                printty("Deep link value extraction completed");
              } else {
                printty(
                    "ERROR: Navigator context is null, cannot process deep link");
              }
            } catch (e) {
              printty("ERROR processing deep link: ${e.toString()}");
              printty("Stack trace: ${StackTrace.current}");
            }
          } else {
            printty("Deep link found but missing required data:");
            if (dp.deepLink?.clickEvent == null) {
              printty("- clickEvent is null");
            }
            if (dp.deepLink?.clickEvent != null &&
                dp.deepLink?.clickEvent["link"] == null) {
              printty("- link is null");
            }
          }
          break;

        case Status.NOT_FOUND:
          printty("Deep link NOT_FOUND - No deep link detected");
          break;

        case Status.ERROR:
          printty("Deep link ERROR: ${dp.error}");
          break;

        case Status.PARSE_ERROR:
          printty("Deep link PARSE_ERROR - Could not parse the deep link");
          break;
      }
    });

    appsflyerSdk.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true);

    await logEvent("appInit", {"App": "App Open"});

    // printty(s.toString());
    printty("===> appflyer initiated...");
  }

  static Future<bool?> logEvent(String eventName, Map? eventValues) async {
    bool? result = false;
    printty(appsflyerSdk.toString());
    printty("-==-=-=-=121");
    try {
      result = await appsflyerSdk.logEvent(eventName, eventValues);
      printty(result.toString());
      printty("app flyer ok=====>");
      return result;
    } on Exception catch (_) {
      return result;
    }
  }
}
